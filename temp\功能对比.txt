// MSSQL存储过程：
USE [hlyy]
GO
/****** Object:  StoredProcedure [dbo].[fx_gytj_zs]    Script Date: 06/08/2025 11:59:17 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- drop proc [fx_gytjfx]
ALTER PROCEDURE [dbo].[fx_gytj_zs]
-- fx_gytj 'fdcbee7e-f6a4-44bd-981c-e61a8086ead9', '380','452591','001'
-- Add the parameters for the stored procedure here
@code  nvarchar(50),
@akb020 nvarchar(20),
@yp_code nvarchar(20),
@yp_tj nvarchar(20)
with recompile
AS
--return 
declare @sda_id nvarchar(10)
declare @by_code nvarchar(10)
declare @n_count int
declare @n_count1 int
declare @n_count2 int
declare @n_count3 int
declare @n_count4 int
declare @n_count5 int
declare @n_count6 int
declare @adm_name nvarchar(50)
declare @n_count7 int
declare @n_count8 int
declare @n_count9 int
declare @n_count10 int
declare @count7 int
declare @by_code_2 nvarchar(10)

declare @zx_flag nvarchar(10)
declare @n_count11 int
declare @n_count12 int
declare @ywa_name nvarchar(50)
declare @gytj_bs nvarchar(10)



select @ywa_name=DRUG_NAME,@zx_flag =zx_flag from itf_hos_drug where DRUG_CODE =@yp_code
select @sda_id=b.sda_id,@gytj_bs=a.gytj_bs from t_sda a, t_byyydzb b  
where a.ID=b.sda_id and  yp_code =@yp_code
select @by_code=by_code from t_tjdzb where   h_tj =@yp_tj
---未对应的药品跳出
if @sda_id =''
begin
return
end

---中药
--if @zx_flag ='3'  
--begin
--return
--end
--中药不需要分析给药途径的跳出
if @zx_flag ='3'  and @gytj_bs='0'
begin
return
end

---免煎颗粒跳出
select @n_count4=COUNT(1) from itf_hos_drug where  DRUG_CODE =@yp_code and zx_flag ='3' and (drug_name like '%颗粒%' or drug_name  like '%免煎%' or drug_name  like '%粉%' or drug_name LIKE '%M%' or drug_name LIKE '%Z%' )
if @n_count4 >0
begin 
return
end 

--中药说明是外用的跳出
select @count7=COUNT(1) from t_pres_med 
where Code =@Code and his_code=@yp_code 
and yysm in ('足浴','外用','打粉')   ;
if @count7 >0
begin 
return
end

select @n_count12=COUNT(1) from t_pres_med 
where Code =@Code and his_code=@yp_code 
and yysm like '%外%'   ;
if @n_count12 >0
begin 
return
end

---西药
select @n_count3 =COUNT(1) from ITF_HOS_DRUG where DRUG_CODE =@yp_code and ZX_FLAG ='3'
--溶媒跳出
select @n_count6 =COUNT(1) from ITF_HOS_DRUG where DRUG_CODE =@yp_code and is_rm ='1'
if @n_count6 >0
begin 
return
end 
---西药没有给药途径的跳出
select @n_count11=COUNT(1) from t_sda_gytj a
where a.sda_id=@sda_id


if @n_count11 =0  and @n_count3 =1 and @yp_tj <>''
begin
select @code, @ywa_name ,'','1','一般提示','RLT003','GYTJJJ','给药途径错误','草药煎药方法错误', 
'【'+@ywa_name+'】无需特殊煎药方式！','0','给药途径错误'
return
end

if @n_count11 =0 
begin
return
end
if (@by_code ='' or @by_code is null )and @n_count3 =0
begin 
return
end

---有了自定义给药途径的跳出
if(exists(select 1 from t_med_zdy_gytj where yp_code=@yp_code and gytj_code=@yp_tj))
begin
return
end
---正确的给药途径跳出
if @n_count3 =1 
begin
select  @n_count=COUNT(1)
from t_sda b , t_sda_gytj a 
where   a.sda_id =b.ID  
and  b.id =@sda_id
and  a.gytj_code =@yp_tj
and a.bs='0'
if @n_count >0
begin 
return
end

end 


if  @n_count3 =0
begin
select  @n_count=COUNT(1)
from t_sda b , t_sda_gytj a 
where   a.sda_id =b.ID  
and  b.id =@sda_id
and  a.gytj_code  in (select by_code from t_tjdzb
where h_tj =@yp_tj)  
and a.bs ='0'  
if @n_count >0
begin 
return
end
end
---开始分析错误的
if @zx_flag ='3'  and @gytj_bs='1' and @yp_tj =''
begin
select @code, @ywa_name ,'','1','一般提示','RLT003','GYTJJJ','给药途径错误','草药煎药方法错误', 
'【'+@ywa_name+'】的煎药方式不能为空，建议使用【'+[dbo].get_gytj(@sda_id)+'】煎药方法','0','给药途径错误'
return
end 

if @n_count3 >0 and @n_count=0 and @gytj_bs='1'
begin 
select @code, @ywa_name ,'','1','一般提示','RLT003','GYTJJJ','给药途径错误','草药煎药方法错误', 
'【'+@ywa_name+'】【中国药典2020版】未提及该煎药方法！，建议使用【'+[dbo].get_gytj(@sda_id)+'】煎药方法','0','给药途径错误'
return

end 


select @n_count10=COUNT(1) from   ITF_HOS_DRUG 
where DRUG_CODE =@yp_code 
and DRUG_NAME like '%醋酸戈舍%'
select @n_count7=count(1) from itf_hos_drug  
where  drug_code =@yp_code  
and DRUG_FOR_NAME not like '%注射%' 
and DRUG_FOR_NAME not like'%针%'
select @by_code_2=substring(by_code,1,2) from t_tjdzb 
where akb020 =@akb020 and h_tj =@yp_tj
select @n_count8=count(1) from t_sda a,t_byyydzb b  
where  b.yp_code =@yp_code and a.ID=b.sda_id  
and (a.ym  like '%胰岛%'  or  a.ym  like '%膏%' )


if @n_count3 =0 
begin 
if (@n_count10 =0 and @n_count7 >0 and @by_code_2 ='02') or (@n_count8>0 and @by_code_2 ='01')
begin 
select @code, @ywa_name ,'','0','一般提示','RLT003','GYTJJJ','给药途径错误','【'+@ywa_name+'】给药途径错误', 
'【'+@ywa_name+'】说明书未提及该给药途径！','0','给药途径错误'
return

end
end

begin 
select @code, @ywa_name ,'','1','一般提示','RLT003','GYTJJJ','给药途径错误','【'+@ywa_name+'】给药途径错误', 
'【'+@ywa_name+'】说明书未提及该给药途径！','0','给药途径错误'
return
end











// MySQL存储过程：
CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_gytj`(IN p_Code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_yp_tj VARCHAR(20))
    READS SQL DATA
    DETERMINISTIC
    COMMENT '给药途径分析存储过程'
main_block: BEGIN
		DECLARE v_sda_id VARCHAR(10);
		DECLARE v_by_code VARCHAR(10);
		DECLARE v_n_count INT;
		DECLARE v_n_count1 INT;
		DECLARE v_n_count2 INT;
		DECLARE v_n_count3 INT;
		DECLARE v_n_count4 INT;
		DECLARE v_n_count5 INT;
		DECLARE v_n_count6 INT;
		DECLARE v_n_count7 INT;
		DECLARE v_n_count8 INT;
		DECLARE v_n_count9 INT;
		DECLARE v_n_count10 INT;
		DECLARE v_n_count11 INT;
		DECLARE v_n_count12 INT;
		DECLARE v_adm_name VARCHAR(50);
		DECLARE v_count7 INT;
		DECLARE v_by_code_2 VARCHAR(10);
		DECLARE v_zx_flag VARCHAR(10);
		DECLARE v_ywa_name VARCHAR(50);
		DECLARE v_gytj_bs VARCHAR(10);
		
		-- 异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				-- 如果出现异常，继续执行
				BEGIN END;
		END;
		
		-- 获取药品基本信息
		SELECT DRUG_NAME, zx_flag INTO v_ywa_name, v_zx_flag 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code
		LIMIT 1;
		
		-- 获取标准数据信息
		SELECT b.sda_id, a.gytj_bs INTO v_sda_id, v_gytj_bs 
		FROM rms_t_sda a, rms_t_byyydzb b 
		WHERE a.ID = b.sda_id AND yp_code = p_yp_code
		LIMIT 1;
		
		-- 获取给药途径编码
		SELECT by_code INTO v_by_code 
		FROM rms_t_tjdzb 
		WHERE h_tj = p_yp_tj
		LIMIT 1;
		
		-- 如果没有标准数据ID则返回
		IF v_sda_id = '' OR v_sda_id IS NULL THEN
				LEAVE main_block;
		END IF;
		
		-- 中药且给药途径标识为0则返回
		IF v_zx_flag = '3' AND v_gytj_bs = '0' THEN
				LEAVE main_block;
		END IF;
		
		-- 检查是否为丸散膏类药品
		SELECT COUNT(1) INTO v_n_count4 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code AND zx_flag = '3' 
		AND (drug_name LIKE '%丸散%' OR drug_name LIKE '%丸%' 
				OR drug_name LIKE '%散%' OR drug_name LIKE '%膏%' 
				OR drug_name LIKE '%胶%');
		
		IF v_n_count4 > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 检查中药外用/煎煮的特殊情况
		SELECT COUNT(1) INTO v_count7 
		FROM rms_t_pres_med 
		WHERE Code = p_Code AND his_code = p_yp_code 
		AND yysm IN ('外用', '煎', '水煎');
		
		IF v_count7 > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 检查中药打粉情况
		SELECT COUNT(1) INTO v_n_count12 
		FROM rms_t_pres_med 
		WHERE Code = p_Code AND his_code = p_yp_code 
		AND yysm LIKE '%打粉%';
		
		IF v_n_count12 > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 检查是否是中药
		SELECT COUNT(1) INTO v_n_count3 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code AND ZX_FLAG = '3';
		
		-- 检查是否为注射剂
		SELECT COUNT(1) INTO v_n_count6 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code AND is_rm = '1';
		
		IF v_n_count6 > 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 检查是否有给药途径设置
		SELECT COUNT(1) INTO v_n_count11 
		FROM rms_t_sda_gytj a
		WHERE a.sda_id = v_sda_id;
		
		-- 如果没有给药途径设置且是中药且给药途径不为空，提示内服不能无煎药方式
		IF v_n_count11 = 0 AND v_n_count3 = 1 AND p_yp_tj != '' THEN
				INSERT INTO rms_t_pres_fx (
						Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				VALUES (
						p_Code, v_ywa_name, '', '1', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', 
						'内服不能无煎药方式', 
						CONCAT('【', v_ywa_name, '】不能缺少煎药方式'), '0', '给药途径错误'
				);
				LEAVE main_block;
		END IF;
		
		-- 如果没有给药途径设置则返回
		IF v_n_count11 = 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 如果给药途径为空且不是中药则返回
		IF (v_by_code = '' OR v_by_code IS NULL) AND v_n_count3 = 0 THEN
				LEAVE main_block;
		END IF;
		
		-- 检查是否有自定义给药途径
		IF EXISTS(SELECT 1 FROM rms_t_med_zdy_gytj WHERE yp_code = p_yp_code AND gytj_code = p_yp_tj) THEN
				LEAVE main_block;
		END IF;
		
		-- 中药给药途径检查
		IF v_n_count3 = 1 THEN
				SELECT COUNT(1) INTO v_n_count
				FROM rms_t_sda b, rms_t_sda_gytj a 
				WHERE a.sda_id = b.ID 
				AND b.id = v_sda_id
				AND a.gytj_code = p_yp_tj
				AND a.bs = '0';
				
				IF v_n_count > 0 THEN
						LEAVE main_block;
				END IF;
		END IF;
		
		-- 西药给药途径检查
		IF v_n_count3 = 0 THEN
				SELECT COUNT(1) INTO v_n_count
				FROM rms_t_sda b, rms_t_sda_gytj a 
				WHERE a.sda_id = b.ID 
				AND b.id = v_sda_id
				AND a.gytj_code IN (
						SELECT by_code FROM rms_t_tjdzb WHERE h_tj = p_yp_tj
				)
				AND a.bs = '0';
				
				IF v_n_count > 0 THEN
						LEAVE main_block;
				END IF;
		END IF;
		
		-- 中药煎药方式为空的处理
		IF v_zx_flag = '3' AND v_gytj_bs = '1' AND p_yp_tj = '' THEN
				INSERT INTO rms_t_pres_fx (
						Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				VALUES (
						p_Code, v_ywa_name, '', '1', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', 
						'内服不能无煎药方式',
						CONCAT('【', v_ywa_name, '】的煎药方式不能为空，建议使用【', rms_get_gytj(v_sda_id), '】煎药方法'), 
						'0', '给药途径错误'
				);
				LEAVE main_block;
		END IF;
		
		-- 中药给药途径不匹配的处理
		IF v_n_count3 > 0 AND v_n_count = 0 AND v_gytj_bs = '1' THEN
				INSERT INTO rms_t_pres_fx (
						Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				VALUES (
						p_Code, v_ywa_name, '', '1', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', 
						'内服不能无煎药方式',
						CONCAT('【', v_ywa_name, '】【中国药典2020版】未提及该煎药方法！，建议使用【', rms_get_gytj(v_sda_id), '】煎药方法'), 
						'0', '给药途径错误'
				);
				LEAVE main_block;
		END IF;
		
		-- 获取其他检查参数
		SELECT COUNT(1) INTO v_n_count10 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code 
		AND DRUG_NAME LIKE '%注射用%';
		
		SELECT COUNT(1) INTO v_n_count7 
		FROM rms_itf_hos_drug 
		WHERE DRUG_CODE = p_yp_code 
		AND DRUG_FOR_NAME NOT LIKE '%胶囊%' 
		AND DRUG_FOR_NAME NOT LIKE '%片%';
		
		SELECT SUBSTRING(by_code, 1, 2) INTO v_by_code_2 
		FROM rms_t_tjdzb 
		WHERE akb020 = p_akb020 AND h_tj = p_yp_tj
		LIMIT 1;
		
		SELECT COUNT(1) INTO v_n_count8 
		FROM rms_t_sda a, rms_t_byyydzb b 
		WHERE b.yp_code = p_yp_code AND a.ID = b.sda_id 
		AND (a.ym LIKE '%注射液%' OR a.ym LIKE '%注%');
		
		-- 西药特殊给药途径检查
		IF v_n_count3 = 0 THEN
				IF (v_n_count10 = 0 AND v_n_count7 > 0 AND v_by_code_2 = '02') 
				OR (v_n_count8 > 0 AND v_by_code_2 = '01') THEN
						INSERT INTO rms_t_pres_fx (
								Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
						)
						VALUES (
								p_Code, v_ywa_name, '', '0', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', 
								CONCAT('【', v_ywa_name, '】给药途径错误'),
								CONCAT('【', v_ywa_name, '】说明书未提及该给药途径'), '0', '给药途径错误'
						);
						LEAVE main_block;
				END IF;
		END IF;
		
		-- 默认给药途径错误处理
		INSERT INTO rms_t_pres_fx (
				Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
		)
		VALUES (
				p_Code, v_ywa_name, '', '1', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', 
				CONCAT('【', v_ywa_name, '】给药途径错误'),
				CONCAT('【', v_ywa_name, '】说明书未提及该给药途径'), '0', '给药途径错误'
		);
END
